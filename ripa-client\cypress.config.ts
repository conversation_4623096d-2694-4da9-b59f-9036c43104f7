import { defineConfig } from 'cypress';
import { addCucumberPreprocessorPlugin } from '@badeball/cypress-cucumber-preprocessor';
import webpackPreprocessor from '@cypress/webpack-preprocessor';
import { addEnvToConfig } from './cypress/plugins';
import path from 'path';
 
export default defineConfig({
  viewportWidth: 1920,
  viewportHeight: 1080,
  defaultCommandTimeout: 30000,
  execTimeout: 30000,
  taskTimeout: 30000,
  pageLoadTimeout: 30000,
  requestTimeout: 20000,
  responseTimeout: 30000,
  numTestsKeptInMemory: 20,
  chromeWebSecurity: false,
  hosts: {
    'demo.contact-dev.com': '127.0.0.1',
    'veri-admin.contact-dev.com': '127.0.0.1'
  },
  e2e: {
    setupNodeEvents: async (on: Cypress.PluginEvents, config: Cypress.PluginConfigOptions) => {
      if (!config.env) {
        config.env = {};
      }
      config.env.stepDefinitions = 'cypress/e2e/step_definitions/**/*.ts';
 
      let processedConfig = await addCucumberPreprocessorPlugin(on, config);
 
      const envModifications = addEnvToConfig(on, processedConfig);
 
      processedConfig = {
        ...processedConfig,
        ...envModifications,
      };
      const options = {
        webpackOptions: {
          resolve: {
            extensions: ['.ts', '.js'],
            alias: {
              "@engine": path.join(__dirname, "./src/engine"),
              "@ducks": path.join(__dirname, "./src/engine/ducks"),
              "@pages": path.join(__dirname, "./src/pages"),
              "@utility": path.join(__dirname, "./src/utility"),
              "@components": path.join(__dirname, "./src/components"),
              "@src": path.join(__dirname, "./src"),
              "@theme": path.join(__dirname, "./src/theme"),
            },
          },
          module: {
            rules: [
              {
                test: /\.ts$/,
                exclude: [/node_modules/],
                use: [
                  {
                    loader: 'ts-loader',
                    options: {
                      transpileOnly: true,
                    },
                  },
                ],
              },
              {
                test: /\.feature$/,
                use: [
                  {
                    loader: '@badeball/cypress-cucumber-preprocessor/webpack',
                    options: processedConfig,
                  },
                ],
              },
            ],
          },
        },
        watchOptions: {},
      };
 
      on('file:preprocessor', webpackPreprocessor(options));
      return processedConfig;
    },
    retries: {
      runMode: 1,
      openMode: 0,
    },
    baseUrl: 'https://demo.contact-dev.com:2222/',
    specPattern: 'cypress/e2e/**/*.feature',
  },
});